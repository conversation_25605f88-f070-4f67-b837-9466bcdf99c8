import { NextRequest, NextResponse } from 'next/server';
import { getSupabaseServerAdminClient } from '@kit/supabase/server-admin-client';
import { getLogger } from '@kit/shared/logger';
import { createTrialConversionService } from '~/lib/services/trial-conversion.service';

/**
 * API endpoint for processing expired trials
 * This can be called by a cron job or scheduled task
 */
export async function POST(request: NextRequest) {
  const logger = await getLogger();
  const client = getSupabaseServerAdminClient();
  const trialService = createTrialConversionService();

  const ctx = {
    name: 'trials.expire',
  };

  logger.info(ctx, 'Processing expired trials...');

  try {
    // Verify the request is authorized (you might want to add API key validation)
    const authHeader = request.headers.get('authorization');
    const expectedToken = process.env.CRON_SECRET || 'your-secret-token';
    
    if (authHeader !== `Bearer ${expectedToken}`) {
      logger.warn(ctx, 'Unauthorized trial expiration request');
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Find all expired trials
    const now = new Date();
    const { data: expiredTrials, error: fetchError } = await client
      .from('subscriptions')
      .select('account_id, id, trial_ends_at')
      .eq('status', 'trialing')
      .eq('active', true)
      .lt('trial_ends_at', now.toISOString());

    if (fetchError) {
      logger.error({ ...ctx, error: fetchError }, 'Failed to fetch expired trials');
      return NextResponse.json({ error: 'Failed to fetch expired trials' }, { status: 500 });
    }

    if (!expiredTrials || expiredTrials.length === 0) {
      logger.info(ctx, 'No expired trials found');
      return NextResponse.json({ 
        message: 'No expired trials found',
        processed: 0 
      });
    }

    logger.info({ ...ctx, count: expiredTrials.length }, 'Found expired trials to process');

    // Process each expired trial
    const results = [];
    for (const trial of expiredTrials) {
      try {
        await trialService.handleTrialExpiration(trial.account_id);
        results.push({
          accountId: trial.account_id,
          subscriptionId: trial.id,
          status: 'success'
        });
      } catch (error) {
        logger.error(
          { ...ctx, accountId: trial.account_id, error },
          'Failed to process expired trial'
        );
        results.push({
          accountId: trial.account_id,
          subscriptionId: trial.id,
          status: 'error',
          error: error instanceof Error ? error.message : 'Unknown error'
        });
      }
    }

    const successCount = results.filter(r => r.status === 'success').length;
    const errorCount = results.filter(r => r.status === 'error').length;

    logger.info(
      { ...ctx, successCount, errorCount, total: results.length },
      'Completed processing expired trials'
    );

    return NextResponse.json({
      message: 'Processed expired trials',
      processed: results.length,
      successful: successCount,
      errors: errorCount,
      results
    });

  } catch (error) {
    logger.error({ ...ctx, error }, 'Failed to process expired trials');
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

/**
 * API endpoint for checking trials near expiration
 * This can be used to send warning notifications
 */
export async function GET(request: NextRequest) {
  const logger = await getLogger();
  const trialService = createTrialConversionService();

  const ctx = {
    name: 'trials.check-expiration',
  };

  logger.info(ctx, 'Checking trials near expiration...');

  try {
    // Verify the request is authorized
    const authHeader = request.headers.get('authorization');
    const expectedToken = process.env.CRON_SECRET || 'your-secret-token';
    
    if (authHeader !== `Bearer ${expectedToken}`) {
      logger.warn(ctx, 'Unauthorized trial check request');
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Get warning days from query params (default to 2)
    const url = new URL(request.url);
    const warningDays = parseInt(url.searchParams.get('warningDays') || '2');

    const expiringTrials = await trialService.checkTrialsNearExpiration(warningDays);

    return NextResponse.json({
      message: 'Checked trials near expiration',
      warningDays,
      count: expiringTrials?.length || 0,
      trials: expiringTrials || []
    });

  } catch (error) {
    logger.error({ ...ctx, error }, 'Failed to check trials near expiration');
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

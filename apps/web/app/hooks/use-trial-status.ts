'use client';

import { useMemo } from 'react';
import { useQuery } from '@rocicorp/zero/react';
import { useZero } from './use-zero';

// TypeScript interfaces for trial system
export interface TrialInfo {
  accountId: string;
  status: TrialStatus;
  startedAt: number | null;
  endsAt: number | null;
  daysRemaining: number | null;
  isExpired: boolean;
  isActive: boolean;
  isConverted: boolean;
  isInactive: boolean;
  progressPercentage: number;
  timeRemaining: {
    days: number;
    hours: number;
    minutes: number;
  } | null;
}

// Trial status enum type matching database enum
export type TrialStatus = 'inactive' | 'active' | 'expired' | 'converted';

export interface UseTrialStatusOptions {
  accountId: string;
  enabled?: boolean;
}

export interface UseTrialStatusReturn {
  trialInfo: TrialInfo | null;
  isLoading: boolean;
  error: Error | null;
  refetch: () => void;
}

/**
 * Hook to get real-time trial status for an account based on subscriptions
 * @param options - Configuration options
 * @returns Trial status information with real-time updates
 */
export function useTrialStatus({
  accountId,
  enabled = true
}: UseTrialStatusOptions): UseTrialStatusReturn {
  const zero = useZero();

  // Query subscriptions for this account to determine trial status
  const subscriptionsQuery = zero.query.subscriptions
    .where('account_id', '=', accountId)
    .orderBy('created_at', 'desc');

  const [subscriptions, subscriptionsDetail] = useQuery(subscriptionsQuery, { enabled });

  // Calculate comprehensive trial information from subscriptions
  const trialInfo = useMemo((): TrialInfo | null => {
    if (!subscriptions || subscriptions.length === 0) {
      // No subscriptions means inactive trial
      return {
        accountId,
        status: 'inactive',
        startedAt: null,
        endsAt: null,
        daysRemaining: null,
        isExpired: false,
        isActive: false,
        isConverted: false,
        isInactive: true,
        progressPercentage: 0,
        timeRemaining: null,
      };
    }

    const now = Date.now();

    // Find the most recent subscription with trial data or active subscription
    const activeSubscription = subscriptions.find(sub => sub.active && sub.status === 'trialing');
    const mostRecentSubscription = subscriptions[0]; // Already ordered by created_at desc

    // Use active trialing subscription if available, otherwise most recent
    const subscription = activeSubscription || mostRecentSubscription;

    // Determine trial status based on subscription
    let status: TrialStatus = 'inactive';
    let startedAt: number | null = null;
    let endsAt: number | null = null;

    if (subscription && subscription.trial_starts_at && subscription.trial_ends_at) {
      startedAt = subscription.trial_starts_at;
      endsAt = subscription.trial_ends_at;

      if (subscription.status === 'trialing') {
        status = now > endsAt ? 'expired' : 'active';
      } else if (subscription.status === 'active' && subscription.trial_ends_at < now) {
        status = 'converted'; // Trial ended and converted to paid
      } else if (subscription.trial_ends_at < now) {
        status = 'expired';
      }
    } else if (subscription && subscription.status === 'active') {
      // Active subscription without trial means converted
      status = 'converted';
    }

    // Calculate time-based properties
    let daysRemaining: number | null = null;
    let isExpired = false;
    let progressPercentage = 0;
    let timeRemaining: TrialInfo['timeRemaining'] = null;

    if (endsAt && startedAt) {
      const totalDuration = endsAt - startedAt;
      const elapsed = now - startedAt;
      const remaining = endsAt - now;

      // Calculate days remaining (can be negative if expired)
      daysRemaining = Math.ceil(remaining / (1000 * 60 * 60 * 24));
      isExpired = remaining <= 0;

      // Calculate progress percentage (0-100)
      progressPercentage = Math.min(100, Math.max(0, (elapsed / totalDuration) * 100));

      // Calculate detailed time remaining
      if (remaining > 0) {
        const days = Math.floor(remaining / (1000 * 60 * 60 * 24));
        const hours = Math.floor((remaining % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60));
        const minutes = Math.floor((remaining % (1000 * 60 * 60)) / (1000 * 60));

        timeRemaining = { days, hours, minutes };
      }
    }

    // Override isExpired if status is already expired
    if (status === 'expired') {
      isExpired = true;
    }

    // Status flags for easy conditional rendering
    const isConverted = status === 'converted';
    const isInactive = status === 'inactive';

    // Determine final status - override if expired
    const finalStatus = isExpired && status === 'active' ? 'expired' : status;

    return {
      accountId,
      status: finalStatus,
      startedAt,
      endsAt,
      daysRemaining,
      isExpired,
      isActive: finalStatus === 'active' && !isExpired,
      isConverted,
      isInactive,
      progressPercentage,
      timeRemaining,
    };
  }, [subscriptions, accountId]);

  // Auto-update expired trials - now handled by subscription status
  // Note: Trial expiration is now managed through subscription status updates
  // The billing provider (Stripe/LemonSqueezy) will handle trial expiration

  return {
    trialInfo,
    isLoading: subscriptionsDetail.type === 'unknown',
    error: null, // Zero doesn't have error states in the result type
    refetch: () => {
      // Zero handles real-time updates automatically, but we can force a refresh if needed
      // This is mainly for error recovery scenarios
    },
  };
}

/**
 * Hook to get trial status for multiple accounts based on subscriptions
 * @param accountIds - Array of account IDs
 * @returns Map of account ID to trial info
 */
export function useMultipleTrialStatuses(accountIds: string[]) {
  const zero = useZero();

  // Query all subscriptions for the given account IDs
  // Note: Zero doesn't support 'in' operator, so we'll need to query each account separately
  // For now, we'll use a simplified approach with the first account ID
  const subscriptionsQuery = zero.query.subscriptions
    .where('account_id', '=', accountIds[0] || '')
    .orderBy('created_at', 'desc');

  const [subscriptions, subscriptionsDetail] = useQuery(subscriptionsQuery);

  const trialInfoMap = useMemo(() => {
    const map = new Map<string, TrialInfo>();

    // Group subscriptions by account_id
    const subscriptionsByAccount = new Map<string, typeof subscriptions>();
    subscriptions.forEach(subscription => {
      if (!subscriptionsByAccount.has(subscription.account_id)) {
        subscriptionsByAccount.set(subscription.account_id, []);
      }
      subscriptionsByAccount.get(subscription.account_id)!.push(subscription);
    });

    // Process each account
    accountIds.forEach(accountId => {
      const accountSubscriptions = subscriptionsByAccount.get(accountId) || [];

      if (accountSubscriptions.length === 0) {
        // No subscriptions means inactive trial
        map.set(accountId, {
          accountId,
          status: 'inactive',
          startedAt: null,
          endsAt: null,
          daysRemaining: null,
          isExpired: false,
          isActive: false,
          isConverted: false,
          isInactive: true,
          progressPercentage: 0,
          timeRemaining: null,
        });
        return;
      }

      const now = Date.now();
      const activeSubscription = accountSubscriptions.find(sub => sub.active && sub.status === 'trialing');
      const mostRecentSubscription = accountSubscriptions[0];
      const subscription = activeSubscription || mostRecentSubscription;

      let status: TrialStatus = 'inactive';
      let startedAt: number | null = null;
      let endsAt: number | null = null;

      if (subscription && subscription.trial_starts_at && subscription.trial_ends_at) {
        startedAt = subscription.trial_starts_at;
        endsAt = subscription.trial_ends_at;

        if (subscription.status === 'trialing') {
          status = now > endsAt ? 'expired' : 'active';
        } else if (subscription.status === 'active' && subscription.trial_ends_at < now) {
          status = 'converted';
        } else if (subscription.trial_ends_at < now) {
          status = 'expired';
        }
      } else if (subscription && subscription.status === 'active') {
        status = 'converted';
      }

      let daysRemaining: number | null = null;
      let isExpired = false;
      let progressPercentage = 0;
      let timeRemaining: TrialInfo['timeRemaining'] = null;

      if (endsAt && startedAt) {
        const totalDuration = endsAt - startedAt;
        const elapsed = now - startedAt;
        const remaining = endsAt - now;

        daysRemaining = Math.ceil(remaining / (1000 * 60 * 60 * 24));
        isExpired = remaining <= 0;
        progressPercentage = Math.min(100, Math.max(0, (elapsed / totalDuration) * 100));

        if (remaining > 0) {
          const days = Math.floor(remaining / (1000 * 60 * 60 * 24));
          const hours = Math.floor((remaining % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60));
          const minutes = Math.floor((remaining % (1000 * 60 * 60)) / (1000 * 60));

          timeRemaining = { days, hours, minutes };
        }
      }

      if (status === 'expired') {
        isExpired = true;
      }

      const isActive = status === 'active' && !isExpired;
      const isConverted = status === 'converted';
      const isInactive = status === 'inactive';

      map.set(accountId, {
        accountId,
        status: isExpired && status === 'active' ? 'expired' : status,
        startedAt,
        endsAt,
        daysRemaining,
        isExpired,
        isActive,
        isConverted,
        isInactive,
        progressPercentage,
        timeRemaining,
      });
    });

    return map;
  }, [subscriptions, accountIds]);

  return {
    trialInfoMap,
    isLoading: subscriptionsDetail.type === 'unknown',
    error: null, // Zero doesn't have error states in the result type
  };
}

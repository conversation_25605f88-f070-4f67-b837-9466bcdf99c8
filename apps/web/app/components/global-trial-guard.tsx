'use client';

import { useEffect, useState } from 'react';
import { usePathname, useRouter } from 'next/navigation';
import { useTrialStatus } from '../hooks/use-trial-status';
import { TrialExpirationModal } from './trial-expiration-modal';

interface GlobalTrialGuardProps {
  accountId: string;
  accountSlug: string;
  children: React.ReactNode;
}

export function GlobalTrialGuard({ accountId, accountSlug, children }: GlobalTrialGuardProps) {
  const router = useRouter();
  const pathname = usePathname();
  const { trialInfo, isLoading } = useTrialStatus({ accountId });
  const [showExpirationModal, setShowExpirationModal] = useState(false);

  // Handle trial expiration with comprehensive enforcement
  useEffect(() => {
    // Don't process while loading
    if (isLoading) return;

    // Don't process if no trial info - this means no subscription data available
    // For new users without subscriptions, they should be redirected to billing
    if (!trialInfo) {
      // Only redirect if not already on billing or auth pages
      if (!pathname.includes('/billing') &&
          !pathname.includes('/auth') &&
          !pathname.includes('/settings')) {
        console.log('⚠️ No trial info (no subscriptions) - redirecting to billing page');
        router.replace(`/home/<USER>/billing`);
      }
      return;
    }

    // Don't process if on allowed pages (billing, settings, account) or auth pages
    if (pathname.includes('/billing') ||
        pathname.includes('/settings') ||
        pathname.includes('/user-settings') ||
        pathname.includes('/auth')) return;

    // Don't process if trial is active or converted (user has access)
    if (trialInfo.isActive || trialInfo.isConverted) return;

    // For expired trials, enforce strict access control - redirect to billing immediately
    if (trialInfo.isExpired) {
      console.log('🚫 Trial expired - enforcing access control, redirecting to billing');
      router.replace(`/home/<USER>/billing`);
      return;
    }

    // Handle inactive trials - redirect to billing to start trial
    if (trialInfo.status === 'inactive') {
      console.log('⚠️ Trial inactive - redirecting to billing page to start trial');
      router.replace(`/home/<USER>/billing`);
      return;
    }
  }, [trialInfo, isLoading, pathname, router, accountSlug]);

  // Handle modal upgrade action
  const handleUpgrade = () => {
    setShowExpirationModal(false);
    router.push(`/home/<USER>/billing`);
  };

  // Show loading state while checking trial status
  if (isLoading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
      </div>
    );
  }

  // Determine if user should have access to the current page
  const isOnAllowedPage = pathname.includes('/billing') ||
                         pathname.includes('/settings') ||
                         pathname.includes('/user-settings') ||
                         pathname.includes('/auth');

  // Block access for users without valid trial/subscription status
  const shouldBlockAccess = trialInfo &&
                           !trialInfo.isActive &&
                           !trialInfo.isConverted &&
                           !isOnAllowedPage;

  // Also block access for users with no trial info (no subscriptions) on non-allowed pages
  const shouldBlockNoSubscription = !trialInfo && !isOnAllowedPage;

  if (shouldBlockAccess || shouldBlockNoSubscription) {
    return (
      <div className="min-h-screen bg-background flex items-center justify-center">
        <div className="text-center space-y-4">
          <h2 className="text-2xl font-semibold">Access Restricted</h2>
          <p className="text-muted-foreground">
            {trialInfo?.isExpired
              ? 'Your trial has expired. Please upgrade to continue using the application.'
              : 'Please start your trial or upgrade to access the application.'
            }
          </p>
          <button
            onClick={() => router.push(`/home/<USER>/billing`)}
            className="px-4 py-2 bg-primary text-primary-foreground rounded-md hover:bg-primary/90"
          >
            {trialInfo?.isExpired ? 'Upgrade Now' : 'Start Trial'}
          </button>
        </div>
      </div>
    );
  }

  return (
    <>
      {children}

      {/* Trial warning modal for users approaching expiration */}
      {trialInfo && trialInfo.isActive && trialInfo.daysRemaining !== null && trialInfo.daysRemaining <= 2 && (
        <TrialExpirationModal
          trialInfo={trialInfo}
          isOpen={showExpirationModal}
          onClose={() => setShowExpirationModal(false)}
          onUpgrade={handleUpgrade}
          onDismiss={() => setShowExpirationModal(false)}
          variant="warning"
        />
      )}
    </>
  );
}

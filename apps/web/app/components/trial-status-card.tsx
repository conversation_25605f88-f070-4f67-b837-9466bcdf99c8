'use client';

import { <PERSON>, <PERSON>, Al<PERSON>Triangle, Zap } from 'lucide-react';

import { Button } from '@kit/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@kit/ui/card';
import { cn } from '@kit/ui/utils';
import { Trans } from '@kit/ui/trans';
import { useTranslation } from 'react-i18next';

import { type TrialInfo } from '../hooks/use-trial-status';
import { getTrialActiveDaysRemainingText } from '../utils/trial-text';

interface TrialStatusCardProps {
  trialInfo: TrialInfo;
  onUpgrade?: () => void;
  onStartTrial?: () => void;
  className?: string;
  compact?: boolean;
}

export function TrialStatusCard({
  trialInfo,
  onUpgrade,
  onStartTrial,
  className,
  compact = false,
}: TrialStatusCardProps) {
  const { t } = useTranslation('trials');

  const getCardContent = () => {
    switch (trialInfo.status) {
      case 'inactive':
        return {
          icon: Zap,
          title: t('startFreeTrial'),
          description: t('startFreeTrialDescription', { period: 7 }),
          action: onStartTrial ? (
            <Button onClick={onStartTrial}>
              <Zap className="mr-2 h-4 w-4" />
              <Trans i18nKey="trials:startTrialButton" values={{ period: 7 }} />
            </Button>
          ) : null,
          variant: 'default' as const,
        };

      case 'active':
        return {
          icon: Clock,
          title: t('trialActive'),
          description: getTrialActiveDaysRemainingText(trialInfo, t) || t('trialActiveDescriptionSoon'),
          action: onUpgrade ? (
            <Button onClick={onUpgrade} >
              <Crown className="mr-2 h-4 w-4" />
              <Trans i18nKey="trials:upgradeNow" />
            </Button>
          ) : null,
          variant: trialInfo.daysRemaining && trialInfo.daysRemaining <= 2 ? 'warning' as const : 'default' as const,
        };

      case 'expired':
        return {
          icon: AlertTriangle,
          title: t('trialExpired'),
          description: t('trialExpiredDescription'),
          action: onUpgrade ? (
            <Button onClick={onUpgrade} variant="destructive">
              <Crown className="mr-2 h-4 w-4" />
              <Trans i18nKey="trials:upgradeToContinue" />
            </Button>
          ) : null,
          variant: 'destructive' as const,
        };

      case 'converted':
        return {  
          icon: Crown,
          title: t('proAccount'),
          description: t('proAccountDescription'),
          action: null,
          variant: 'success' as const,
        };

      default:
        return null;
    }
  };

  const content = getCardContent();
  if (!content) return null;

  const { icon: Icon, title, description, action, variant } = content;

  if (compact) {
    return (
      <div className={cn(
        'flex items-center justify-between p-3 rounded-lg border',
        variant === 'warning' && 'bg-yellow-50 border-yellow-200 dark:bg-yellow-500/10 dark:border-yellow-500/20',
        variant === 'destructive' && 'bg-red-50 border-red-200 dark:bg-red-500/10 dark:border-red-500/20',
        variant === 'success' && 'bg-green-50 border-green-200 dark:bg-green-500/10 dark:border-green-500/20',
        variant === 'default' && 'bg-card border-border',
        className
      )}>
        <div className="flex items-center gap-3">
          <Icon className={cn(
            'h-5 w-5',
            variant === 'warning' && 'text-yellow-600 dark:text-yellow-400',
            variant === 'destructive' && 'text-red-600 dark:text-red-400',
            variant === 'success' && 'text-green-600 dark:text-green-400',
            variant === 'default' && 'text-muted-foreground'
          )} />
          <div>
            <p className="font-medium text-sm">{title}</p>
            <p className="text-xs text-muted-foreground">{description}</p>
          </div>
        </div>
        {action && <div className="ml-4">{action}</div>}
      </div>
    );
  }

  return (
    <Card className={cn(
      'transition-all duration-200',
      variant === 'warning' && 'border-yellow-200 bg-yellow-50/50 dark:border-yellow-500/20 dark:bg-yellow-500/5',
      variant === 'destructive' && 'border-red-200 bg-red-50/50 dark:border-red-500/20 dark:bg-red-500/5',
      variant === 'success' && 'border-green-200 bg-green-50/50 dark:border-green-500/20 dark:bg-green-500/5',
      className
    )}>
      <CardHeader className="pb-3">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-3">
            <div className={cn(
              'p-2 rounded-lg',
              variant === 'warning' && 'bg-yellow-100 dark:bg-yellow-500/20',
              variant === 'destructive' && 'bg-red-100 dark:bg-red-500/20',
              variant === 'success' && 'bg-green-100 dark:bg-green-500/20',
              variant === 'default' && 'bg-muted'
            )}>
              <Icon className={cn(
                'h-5 w-5',
                variant === 'warning' && 'text-yellow-600 dark:text-yellow-400',
                variant === 'destructive' && 'text-red-600 dark:text-red-400',
                variant === 'success' && 'text-green-600 dark:text-green-400',
                variant === 'default' && 'text-muted-foreground'
              )} />
            </div>
            <div>
              <CardTitle className="text-lg">{title}</CardTitle>
              <CardDescription className="mt-1">{description}</CardDescription>
            </div>
          </div>
          {/* <TrialBadge trialInfo={trialInfo} size="sm" /> */}
        </div>
      </CardHeader>

      {/* {trialInfo.status === 'active' && (
        <CardContent className="pt-0">
          <div className="space-y-3">
            <div className="flex items-center justify-between text-sm">
              <span className="text-muted-foreground">Trial Progress</span>
              <span className="font-medium">
                {trialInfo.timeRemaining ? (
                  `${trialInfo.timeRemaining.days}d ${trialInfo.timeRemaining.hours}h ${trialInfo.timeRemaining.minutes}m left`
                ) : (
                  'Calculating...'
                )}
              </span>
            </div>
            <Progress 
              value={trialInfo.progressPercentage} 
              className={cn(
                'h-2',
                trialInfo.progressPercentage >= 80 && '[&>div]:bg-red-500',
                trialInfo.progressPercentage >= 60 && trialInfo.progressPercentage < 80 && '[&>div]:bg-yellow-500',
                trialInfo.progressPercentage < 60 && '[&>div]:bg-blue-500'
              )}
            />
          </div>
        </CardContent>
      )} */}

      {action && (
        <CardContent className={cn(trialInfo.status === 'active' ? 'pt-0' : '')}>
          <div className="flex gap-2">
            {action}
            {/* {trialInfo.status === 'active' && (
              <Button variant="outline" size="sm" className="flex-1">
                Learn More
                <ArrowRight className="ml-2 h-3 w-3" />
              </Button>
            )} */}
          </div>
        </CardContent>
      )}
    </Card>
  );
}

interface TrialStatusSummaryProps {
  trialInfo: TrialInfo;
  className?: string;
}

export function TrialStatusSummary({ trialInfo, className }: TrialStatusSummaryProps) {
  const { t } = useTranslation('trials');

  const getStatusColor = () => {
    switch (trialInfo.status) {
      case 'active':
        return trialInfo.daysRemaining && trialInfo.daysRemaining <= 2 
          ? 'text-yellow-600 dark:text-yellow-400'
          : 'text-blue-600 dark:text-blue-400';
      case 'expired':
        return 'text-red-600 dark:text-red-400';
      case 'converted':
        return 'text-green-600 dark:text-green-400';
      default:
        return 'text-muted-foreground';
    }
  };

  const getStatusText = () => {
    switch (trialInfo.status) {
      case 'active':
        return getTrialActiveDaysRemainingText(trialInfo, t) || t('trialActive');
      case 'expired':
        return t('trialExpired');
      case 'converted':
        return t('proAccount');
      case 'inactive':
        return t('noActiveTrial');
      default:
        return t('status.inactive.description');
    }
  };

  return (
    <div className={cn('flex items-center gap-2', className)}>
      <div className={cn('w-2 h-2 rounded-full', getStatusColor().replace('text-', 'bg-'))} />
      <span className={cn('text-sm font-medium', getStatusColor())}>
        {getStatusText()}
      </span>
    </div>
  );
}

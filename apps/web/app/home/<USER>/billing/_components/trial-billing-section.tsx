'use client';

import { useRouter } from 'next/navigation';
import { useZero } from '~/hooks/use-zero';
import { useTrialStatus } from '~/hooks/use-trial-status';
import { TrialStatusCard } from '~/components/trial-status-card';

interface TrialBillingSectionProps {
  accountId: string;
  accountSlug: string;
}

export function TrialBillingSection({ accountId, accountSlug }: TrialBillingSectionProps) {
  const router = useRouter();
  const zero = useZero();
  const { trialInfo, isLoading } = useTrialStatus({ accountId });

  const handleStartTrial = async () => {
    try {
      // Use the new subscription-based trial mutation
      await zero.mutate.accounts.startTrial({
        accountId,
        trialDays: 7,
        planId: 'trial', // Default trial plan
      });

      // Optionally refresh the page or show success message
      console.log('Trial started successfully');
    } catch (error) {
      console.error('Failed to start trial:', error);
      // TODO: Show user-friendly error message
    }
  };

  const handleUpgrade = () => {
    // Scroll to checkout section or redirect to upgrade flow
    const checkoutElement = document.getElementById('checkout-section');
    if (checkoutElement) {
      checkoutElement.scrollIntoView({ behavior: 'smooth' });
    }
  };

  const handleConvertTrial = async () => {
    try {
      // Use the new subscription-based trial conversion
      await zero.mutate.accounts.convertTrial({
        accountId,
        // newSubscriptionId can be provided if we have a specific subscription to convert to
        // planId can be provided to specify which plan to convert to
      });

      console.log('Trial converted successfully');
    } catch (error) {
      console.error('Failed to convert trial:', error);
      // TODO: Show user-friendly error message
    }
  };

  if (isLoading || !trialInfo) {
    return null;
  }

  // Only show trial card for accounts that have trial functionality
  if (trialInfo.status === 'converted') {
    return null; // Don't show for already converted accounts
  }

  return (
    <div className="mb-6">
      <TrialStatusCard
        trialInfo={trialInfo}
        onUpgrade={handleUpgrade}
        onStartTrial={trialInfo.status === 'inactive' ? handleStartTrial : undefined}
      />
    </div>
  );
}

import { redirect } from 'next/navigation';
import type { NextRequest } from 'next/server';

import { createAuthCallbackService } from '@kit/supabase/auth';
import { getSupabaseServerClient } from '@kit/supabase/server-client';

import pathsConfig from '~/config/paths.config';

export async function GET(request: NextRequest) {
  const service = createAuthCallbackService(getSupabaseServerClient());
  const client = getSupabaseServerClient();

  const { nextPath } = await service.exchangeCodeForSession(request, {
    joinTeamPath: pathsConfig.app.joinTeam,
    redirectPath: pathsConfig.app.home,
  });

  // Check if this is a new user without subscriptions and redirect to billing
  try {
    const { data: { user } } = await client.auth.getUser();

    if (user) {
      // Check if user has any subscriptions or billing customers
      const { data: subscriptions } = await client
        .from('subscriptions')
        .select('id, account_id')
        .eq('account_id', user.id)
        .limit(1);

      const { data: billingCustomers } = await client
        .from('billing_customers')
        .select('id, account_id')
        .eq('account_id', user.id)
        .limit(1);

      // If user has no subscriptions and no billing customers, redirect to billing
      if ((!subscriptions || subscriptions.length === 0) &&
          (!billingCustomers || billingCustomers.length === 0)) {

        // Check if this is a personal account (not team account)
        const { data: account } = await client
          .from('accounts')
          .select('is_personal_account, slug')
          .eq('id', user.id)
          .single();

        if (account) {
          if (account.is_personal_account) {
            // Redirect personal accounts to personal billing page
            return redirect('/home/<USER>');
          } else if (account.slug) {
            // Redirect team accounts to team billing page
            return redirect(`/home/<USER>/billing`);
          }
        }
      }
    }
  } catch (error) {
    // If there's an error checking subscriptions, continue with normal flow
    console.error('Error checking user subscriptions in auth callback:', error);
  }

  return redirect(nextPath);
}

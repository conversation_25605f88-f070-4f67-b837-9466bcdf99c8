import 'server-only';

import { getSupabaseServerAdminClient } from '@kit/supabase/server-admin-client';
import { getLogger } from '@kit/shared/logger';

/**
 * Service for handling trial conversion logic
 */
export class TrialConversionService {
  private readonly namespace = 'trial-conversion';

  /**
   * Handle trial expiration - called when a trial period ends
   * This can be triggered by webhooks or scheduled jobs
   */
  async handleTrialExpiration(accountId: string) {
    const client = getSupabaseServerAdminClient();
    const logger = await getLogger();

    const ctx = {
      namespace: this.namespace,
      accountId,
    };

    logger.info(ctx, 'Processing trial expiration...');

    try {
      // Find active trial subscriptions for this account
      const { data: trialSubscriptions, error: fetchError } = await client
        .from('subscriptions')
        .select('*')
        .eq('account_id', accountId)
        .eq('status', 'trialing')
        .eq('active', true);

      if (fetchError) {
        logger.error({ ...ctx, error: fetchError }, 'Failed to fetch trial subscriptions');
        throw new Error('Failed to fetch trial subscriptions');
      }

      if (!trialSubscriptions || trialSubscriptions.length === 0) {
        logger.info(ctx, 'No active trial subscriptions found');
        return;
      }

      // Process each trial subscription
      for (const subscription of trialSubscriptions) {
        const now = new Date();
        const trialEndsAt = new Date(subscription.trial_ends_at);

        // Check if trial has actually expired
        if (trialEndsAt > now) {
          logger.info(
            { ...ctx, subscriptionId: subscription.id, trialEndsAt },
            'Trial has not expired yet'
          );
          continue;
        }

        logger.info(
          { ...ctx, subscriptionId: subscription.id },
          'Trial has expired, updating subscription status'
        );

        // Update subscription status to canceled (expired trial)
        const { error: updateError } = await client
          .from('subscriptions')
          .update({
            status: 'canceled',
            active: false,
            updated_at: now.toISOString(),
          })
          .eq('id', subscription.id);

        if (updateError) {
          logger.error(
            { ...ctx, subscriptionId: subscription.id, error: updateError },
            'Failed to update expired trial subscription'
          );
          continue;
        }

        logger.info(
          { ...ctx, subscriptionId: subscription.id },
          'Successfully updated expired trial subscription'
        );
      }
    } catch (error) {
      logger.error({ ...ctx, error }, 'Failed to process trial expiration');
      throw error;
    }
  }

  /**
   * Handle trial conversion to paid subscription
   * This is typically called from billing provider webhooks
   */
  async handleTrialConversion(params: {
    accountId: string;
    subscriptionId: string;
    newStatus: 'active' | 'past_due' | 'canceled';
    billingProvider: 'stripe' | 'lemon-squeezy';
  }) {
    const client = getSupabaseServerAdminClient();
    const logger = await getLogger();

    const ctx = {
      namespace: this.namespace,
      ...params,
    };

    logger.info(ctx, 'Processing trial conversion...');

    try {
      // Update the subscription status
      const { error: updateError } = await client
        .from('subscriptions')
        .update({
          status: params.newStatus,
          active: params.newStatus === 'active',
          // Clear trial dates as it's now a paid subscription
          trial_starts_at: null,
          trial_ends_at: null,
          updated_at: new Date().toISOString(),
        })
        .eq('id', params.subscriptionId)
        .eq('account_id', params.accountId);

      if (updateError) {
        logger.error({ ...ctx, error: updateError }, 'Failed to update subscription for trial conversion');
        throw new Error('Failed to update subscription for trial conversion');
      }

      logger.info(ctx, 'Successfully converted trial to paid subscription');
    } catch (error) {
      logger.error({ ...ctx, error }, 'Failed to process trial conversion');
      throw error;
    }
  }

  /**
   * Handle payment failure during trial conversion
   */
  async handleTrialPaymentFailure(params: {
    accountId: string;
    subscriptionId: string;
    billingProvider: 'stripe' | 'lemon-squeezy';
  }) {
    const client = getSupabaseServerAdminClient();
    const logger = await getLogger();

    const ctx = {
      namespace: this.namespace,
      ...params,
    };

    logger.info(ctx, 'Processing trial payment failure...');

    try {
      // Update subscription status to past_due or canceled depending on provider logic
      const { error: updateError } = await client
        .from('subscriptions')
        .update({
          status: 'past_due',
          active: false,
          updated_at: new Date().toISOString(),
        })
        .eq('id', params.subscriptionId)
        .eq('account_id', params.accountId);

      if (updateError) {
        logger.error({ ...ctx, error: updateError }, 'Failed to update subscription for payment failure');
        throw new Error('Failed to update subscription for payment failure');
      }

      logger.info(ctx, 'Successfully handled trial payment failure');

      // TODO: Send notification to user about payment failure
      // TODO: Implement retry logic or grace period
    } catch (error) {
      logger.error({ ...ctx, error }, 'Failed to process trial payment failure');
      throw error;
    }
  }

  /**
   * Check for trials that are about to expire and send warnings
   */
  async checkTrialsNearExpiration(warningDays: number = 2) {
    const client = getSupabaseServerAdminClient();
    const logger = await getLogger();

    const ctx = {
      namespace: this.namespace,
      warningDays,
    };

    logger.info(ctx, 'Checking for trials near expiration...');

    try {
      const warningDate = new Date();
      warningDate.setDate(warningDate.getDate() + warningDays);

      const { data: expiringTrials, error: fetchError } = await client
        .from('subscriptions')
        .select('*, accounts(name, email)')
        .eq('status', 'trialing')
        .eq('active', true)
        .lte('trial_ends_at', warningDate.toISOString())
        .gt('trial_ends_at', new Date().toISOString());

      if (fetchError) {
        logger.error({ ...ctx, error: fetchError }, 'Failed to fetch expiring trials');
        throw new Error('Failed to fetch expiring trials');
      }

      if (!expiringTrials || expiringTrials.length === 0) {
        logger.info(ctx, 'No trials near expiration found');
        return;
      }

      logger.info({ ...ctx, count: expiringTrials.length }, 'Found trials near expiration');

      // TODO: Send warning notifications to users
      // TODO: Implement email/in-app notification system

      return expiringTrials;
    } catch (error) {
      logger.error({ ...ctx, error }, 'Failed to check trials near expiration');
      throw error;
    }
  }
}

/**
 * Create a new instance of the TrialConversionService
 */
export function createTrialConversionService() {
  return new TrialConversionService();
}
